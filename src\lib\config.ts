// Vercel-only deployment configuration
export const config = {
  // API Base URL - Static deployment only
  apiBaseUrl: '',

  // Features available in Vercel static deployment
  features: {
    whatsappIntegration: false, // Disabled - requires backend server
    realTimeSync: false, // Disabled - requires backend server
    mediaUpload: false, // Disabled - requires backend server
    qrCodeGeneration: false, // Disabled - requires backend server
    contactManagement: true, // Frontend-only functionality
    templateManagement: true, // Frontend-only functionality
    campaignPlanning: true, // Frontend-only functionality
    analytics: true, // Frontend-only functionality
  },

  // Vercel deployment environment
  environment: {
    isVercel: true,
    isProduction: true,
    isDevelopment: false,
    deploymentOnly: true, // Only works on Vercel
  },

  // Deployment validation
  validateEnvironment() {
    // Check if running on Vercel
    const isVercelDeployment = typeof window !== 'undefined' &&
      (window.location.hostname.includes('vercel.app') ||
       window.location.hostname.includes('vercel.com'));

    if (!isVercelDeployment && typeof window !== 'undefined') {
      console.warn('⚠️ This application is configured for Vercel deployment only.');
      console.warn('🌐 Visit: https://whatsapp-rdwablyh-ankits-projects-72848ab0.vercel.app');
    }

    return isVercelDeployment;
  }
};

// Helper function to check if a feature is available
export const isFeatureAvailable = (feature: keyof typeof config.features): boolean => {
  return config.features[feature];
};

// Helper function to get appropriate API endpoint
export const getApiUrl = (endpoint: string): string => {
  return `${config.apiBaseUrl}/api${endpoint}`;
};

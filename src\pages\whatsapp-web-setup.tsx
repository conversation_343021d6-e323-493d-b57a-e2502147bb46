import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useWhatsappNumbers } from '@/hooks/useData';
import Sidebar from '@/components/layout/sidebar';
import Header from '@/components/layout/header';
import {
  QrCode,
  Smartphone,
  CheckCircle,
  XCircle,
  RefreshCw,
  Phone,
  MessageCircle,
  Wifi,
  WifiOff,
  Settings
} from 'lucide-react';

interface WhatsAppWebSession {
  sessionId: string;
  status: string;
  phoneNumber?: string;
  userId: string;
  createdAt: string;
  lastActivity?: string;
}

export default function WhatsAppWebSetup() {
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<string>('disconnected');
  const [connectedPhone, setConnectedPhone] = useState<string | null>(null);
  const { toast } = useToast();

  // Fetch WhatsApp numbers using mock data
  const { data: whatsappNumbers = [], isLoading: sessionsLoading } = useWhatsappNumbers();

  // Convert WhatsApp numbers to sessions format for compatibility
  const sessions = whatsappNumbers.map(number => ({
    sessionId: `session_${number.id}`,
    status: number.status,
    phoneNumber: number.phone,
    userId: 'admin-user-123',
    createdAt: number.connected_at || new Date().toISOString(),
    lastActivity: number.last_seen || new Date().toISOString(),
  }));

  // Mock functions
  const generateQRCode = () => {
    setCurrentSessionId('mock-session-123');
    setQrCode('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ3aGl0ZSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9ImJsYWNrIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+TW9jayBRUiBDb2RlPC90ZXh0Pgo8L3N2Zz4K');
    setConnectionStatus('qr_ready');
    toast({
      title: "QR Code Generated",
      description: "Scan the QR code with your WhatsApp mobile app",
    });
  };

  const disconnectSession = (sessionId: string) => {
    setCurrentSessionId(null);
    setConnectionStatus('disconnected');
    setConnectedPhone(null);
    setQrCode(null);
    toast({
      title: "Disconnected",
      description: "WhatsApp session has been disconnected",
    });
  };

  const cleanDuplicates = () => {
    toast({
      title: "Duplicates Cleaned",
      description: "Removed 0 duplicate WhatsApp numbers",
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge className="bg-green-500 text-white"><CheckCircle className="w-3 h-3 mr-1" />Connected</Badge>;
      case 'disconnected':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Disconnected</Badge>;
      case 'qr_ready':
        return <Badge className="bg-blue-500 text-white"><QrCode className="w-3 h-3 mr-1" />QR Ready</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-background via-background-secondary to-background-tertiary">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header
          title="WhatsApp Web Setup"
          subtitle="Connect your WhatsApp using whatsapp-web.js"
          primaryAction={{
            label: "Session Cleanup",
            onClick: () => {},
            icon: Settings
          }}
        />
        <main className="flex-1 overflow-auto p-6 space-y-8">
          {/* Active Sessions */}
          <Card className="border-2 border-primary/20 shadow-lg">
            <CardHeader className="flex flex-row items-center space-y-0 pb-4">
              <Phone className="w-6 h-6 text-primary mr-3" />
              <div>
                <CardTitle className="text-xl">Active WhatsApp Sessions</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              {sessionsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="w-6 h-6 animate-spin mr-2" />
                  <span>Loading sessions...</span>
                </div>
              ) : sessions.length > 0 ? (
                <div className="space-y-4">
                  {sessions.map((session) => (
                    <div key={session.sessionId} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <Smartphone className="w-8 h-8 text-primary" />
                        <div>
                          <p className="font-medium">{session.phoneNumber}</p>
                          <p className="text-sm text-muted-foreground">
                            Session: {session.sessionId}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Last activity: {formatDate(session.lastActivity || session.createdAt)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        {getStatusBadge(session.status)}
                        {session.status === 'connected' ? (
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => disconnectSession(session.sessionId)}
                          >
                            <XCircle className="w-4 h-4 mr-1" />
                            Disconnect
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={generateQRCode}
                          >
                            <RefreshCw className="w-4 h-4 mr-1" />
                            Reconnect
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Smartphone className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg font-medium text-muted-foreground mb-2">No active WhatsApp sessions</p>
                  <p className="text-sm text-muted-foreground">Connect a new WhatsApp number to get started</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Connect New Number */}
          <Card className="border-2 border-green-500/20 shadow-lg">
            <CardHeader className="flex flex-row items-center space-y-0 pb-4">
              <MessageCircle className="w-6 h-6 text-green-600 mr-3" />
              <div>
                <CardTitle className="text-xl">Connect New WhatsApp Number</CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Connect your WhatsApp number using the official WhatsApp Web protocol
                </p>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex space-x-4">
                <Button
                  onClick={generateQRCode}
                  className="bg-green-600 hover:bg-green-700 text-white"
                  disabled={sessionsLoading}
                >
                  <QrCode className="w-4 h-4 mr-2" />
                  Generate QR Code
                </Button>
                <Button
                  variant="outline"
                  onClick={cleanDuplicates}
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Clean Duplicates
                </Button>
              </div>

              {qrCode && (
                <div className="border-2 border-dashed border-green-500/30 rounded-lg p-6 text-center">
                  <div className="mb-4">
                    <img
                      src={qrCode}
                      alt="WhatsApp QR Code"
                      className="mx-auto border rounded-lg shadow-md"
                      style={{ width: '200px', height: '200px' }}
                    />
                  </div>
                  <div className="space-y-2">
                    <p className="font-medium text-green-700">QR Code Ready!</p>
                    <p className="text-sm text-muted-foreground">
                      1. Open WhatsApp on your phone<br/>
                      2. Go to Settings → Linked Devices<br/>
                      3. Tap "Link a Device" and scan this QR code
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Connection Status */}
          <Alert>
            <div className="flex items-center">
              {sessions.some(s => s.status === 'connected') ? (
                <Wifi className="w-4 h-4 text-green-600" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-600" />
              )}
              <AlertDescription className="ml-2">
                {sessions.some(s => s.status === 'connected')
                  ? `Connected: ${sessions.filter(s => s.status === 'connected').length} active session(s)`
                  : 'No active WhatsApp connections'
                }
              </AlertDescription>
            </div>
          </Alert>
        </main>
      </div>
    </div>
  );
}

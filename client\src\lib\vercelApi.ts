// Vercel-compatible API client
const API_BASE = process.env.NODE_ENV === 'production' 
  ? '' // Vercel will handle routing
  : 'http://localhost:5000';

class VercelApiClient {
  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${API_BASE}/api${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Health check
  async healthCheck() {
    return this.request('/health');
  }

  // Contacts
  async getContacts() {
    return this.request('/contacts');
  }

  async addContact(contact: { name: string; phone: string; email?: string; tags?: string[] }) {
    return this.request('/contacts', {
      method: 'POST',
      body: JSON.stringify(contact),
    });
  }

  // Templates
  async getTemplates() {
    return this.request('/templates');
  }

  // WhatsApp status (limited on Vercel)
  async getWhatsAppStatus() {
    return this.request('/whatsapp/status');
  }
}

export const vercelApi = new VercelApiClient();

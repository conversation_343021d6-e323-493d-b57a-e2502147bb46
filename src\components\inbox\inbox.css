/* Advanced WhatsApp Inbox Layout */
.inbox-layout-stable {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Advanced sidebar with glassmorphism */
.inbox-sidebar-stable {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: clamp(320px, 28vw, 420px);
  min-width: 320px;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(226, 232, 240, 0.5);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
}

.inbox-sidebar-stable::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

/* Advanced chat area */
.inbox-chat-stable {
  height: 100vh;
  overflow: hidden;
  position: relative;
  flex: 1;
  min-width: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  isolation: isolate;
}

/* Advanced messages container */
.inbox-messages-container {
  height: calc(100vh - 180px);
  overflow: hidden;
  position: relative;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  flex: 1;
}

.inbox-scrollable {
  height: 100%;
  overflow-y: auto;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
}

.inbox-scrollable::-webkit-scrollbar {
  width: 6px;
}

.inbox-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.inbox-scrollable::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.inbox-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

.inbox-input-container {
  position: sticky;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* Fixed header styles */
.inbox-header-fixed {
  position: sticky;
  top: 0;
  z-index: 20;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.inbox-chat-header-fixed {
  position: sticky !important;
  top: 0 !important;
  z-index: 100 !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(15px) !important;
  border-bottom: 2px solid rgba(59, 130, 246, 0.2) !important;
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.15) !important;
  min-height: 90px !important;
  transition: none !important;
  will-change: auto !important;
  width: 100% !important;
  flex-shrink: 0 !important;
  margin: 0 !important;
  padding: 1rem 1.5rem !important;
}

.inbox-chat-header-fixed:hover {
  background: rgba(255, 255, 255, 0.98) !important;
  box-shadow: 0 6px 12px -2px rgba(0, 0, 0, 0.15) !important;
}

/* Font styling consistent with application */
.inbox-container * {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  color: #000000 !important;
}

/* Responsive font sizes */
.inbox-conversation-item h3 {
  font-weight: 600 !important;
  font-size: clamp(0.875rem, 2vw, 1rem) !important;
  color: #000000 !important;
  line-height: 1.4 !important;
}

.inbox-conversation-item p {
  font-size: clamp(0.75rem, 1.8vw, 0.875rem) !important;
  color: #666666 !important;
  line-height: 1.3 !important;
}

.inbox-chat-header h3 {
  font-weight: 600 !important;
  font-size: clamp(1rem, 2.5vw, 1.125rem) !important;
  color: #000000 !important;
  line-height: 1.3 !important;
}

.inbox-chat-header p {
  font-size: clamp(0.875rem, 2vw, 1rem) !important;
  color: #666666 !important;
  line-height: 1.4 !important;
}

.inbox-message-text {
  font-size: clamp(0.875rem, 2vw, 1rem) !important;
  line-height: 1.5 !important;
  color: #000000 !important;
}

.inbox-message-time {
  font-size: clamp(0.75rem, 1.8vw, 0.875rem) !important;
  color: #666666 !important;
}

/* Advanced Conversation Item Styles with Animations */
.inbox-conversation-item {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 20px !important;
  margin: 6px 12px !important;
  position: relative !important;
  overflow: hidden !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%) !important;
  border: 1px solid rgba(226, 232, 240, 0.6) !important;
  backdrop-filter: blur(10px) !important;
  animation: slideInConversation 0.5s ease-out !important;
  cursor: pointer !important;
}

@keyframes slideInConversation {
  from {
    opacity: 0;
    transform: translateX(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.inbox-conversation-item::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg,
    transparent,
    rgba(59, 130, 246, 0.15),
    rgba(139, 92, 246, 0.1),
    transparent
  ) !important;
  transition: left 0.6s ease !important;
  z-index: 1 !important;
}

.inbox-conversation-item:hover::before {
  left: 100% !important;
}

.inbox-conversation-item:hover {
  transform: translateY(-4px) scale(1.02) !important;
  box-shadow:
    0 12px 40px rgba(59, 130, 246, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.1) !important;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(139, 92, 246, 0.05) 100%) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
}

.inbox-conversation-item:active {
  transform: translateY(-2px) scale(1.01) !important;
  transition: all 0.1s ease !important;
}

/* Selected conversation state */
.inbox-conversation-item.selected {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(139, 92, 246, 0.1) 100%) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px) !important;
}

/* Avatar animations */
.inbox-conversation-item .avatar {
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 2 !important;
}

.inbox-conversation-item:hover .avatar {
  transform: scale(1.1) rotate(5deg) !important;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3) !important;
}

/* Content animations */
.inbox-conversation-item .content {
  position: relative !important;
  z-index: 2 !important;
  transition: all 0.3s ease !important;
}

.inbox-conversation-item:hover .content {
  transform: translateX(4px) !important;
}

/* Enhanced customer details in header */
.inbox-chat-header h3 {
  font-weight: 600 !important;
  font-size: 1.125rem !important;
  color: #000000 !important;
  margin-bottom: 0.25rem !important;
}

.inbox-chat-header p {
  font-size: 0.875rem !important;
  color: #666666 !important;
  margin: 0 !important;
}

.inbox-chat-header .text-xs {
  font-size: 0.75rem !important;
  color: #888888 !important;
}

/* Ensure fixed header stays on top during scroll */
.inbox-chat-stable .inbox-chat-header-fixed {
  position: sticky !important;
  top: 0 !important;
  z-index: 100 !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(15px) !important;
  border-bottom: 2px solid rgba(59, 130, 246, 0.2) !important;
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.15) !important;
  min-height: 90px !important;
  transition: none !important;
  will-change: auto !important;
  transform: none !important;
  width: 100% !important;
  flex-shrink: 0 !important;
  margin: 0 !important;
  padding: 1rem 1.5rem !important;
}

/* Online status indicator */
.inbox-chat-header .bg-green-500 {
  background-color: #10b981 !important;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3) !important;
}

/* Avatar styling in header */
.inbox-chat-header-fixed .h-12 {
  border: 2px solid rgba(59, 130, 246, 0.2) !important;
  transition: all 0.3s ease !important;
}

.inbox-chat-header-fixed .h-12:hover {
  border-color: rgba(59, 130, 246, 0.4) !important;
  transform: scale(1.05) !important;
}

/* Button styling in header */
.inbox-chat-header-fixed button {
  transition: all 0.3s ease !important;
}

.inbox-chat-header-fixed button:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
  transform: scale(1.1) !important;
}

/* Responsive Design for Different Screen Sizes */
@media (max-width: 1200px) {
  .inbox-sidebar-stable {
    width: clamp(260px, 30vw, 320px);
  }

  .inbox-conversation-item h3 {
    font-size: clamp(0.8rem, 2.2vw, 0.95rem) !important;
  }

  .inbox-chat-header h3 {
    font-size: clamp(0.95rem, 2.8vw, 1.1rem) !important;
  }
}

@media (max-width: 768px) {
  .inbox-layout-stable {
    flex-direction: column;
  }

  .inbox-sidebar-stable {
    width: 100%;
    height: 40vh;
    min-width: unset;
  }

  .inbox-chat-stable {
    height: 60vh;
  }

  .inbox-messages-container {
    height: calc(60vh - 100px);
  }

  .inbox-chat-header-fixed {
    min-height: 70px !important;
    padding: 0.75rem !important;
  }

  .inbox-conversation-item {
    padding: 0.75rem !important;
  }
}

@media (max-width: 480px) {
  .inbox-sidebar-stable {
    height: 35vh;
  }

  .inbox-chat-stable {
    height: 65vh;
  }

  .inbox-chat-header-fixed {
    min-height: 60px !important;
    padding: 0.5rem !important;
  }

  .inbox-conversation-item h3 {
    font-size: 0.85rem !important;
  }

  .inbox-conversation-item p {
    font-size: 0.75rem !important;
  }
}

/* Auto-adjust for browser zoom */
@media (min-resolution: 1.5dppx) {
  .inbox-container * {
    font-size: calc(1em * 0.9) !important;
  }
}

@media (min-resolution: 2dppx) {
  .inbox-container * {
    font-size: calc(1em * 0.85) !important;
  }
}

/* Smooth transitions for responsive changes */
.inbox-layout-stable,
.inbox-sidebar-stable,
.inbox-chat-stable,
.inbox-messages-container {
  transition: all 0.3s ease !important;
}

/* Ensure proper text rendering at all zoom levels */
.inbox-container {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Sliding header animations */
.sliding-header {
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.sliding-header.hidden {
  transform: translateY(-100%);
}

.sliding-header.visible {
  transform: translateY(0);
}

/* Content area adjustments for sliding header */
.content-with-sliding-header {
  transition: padding-top 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-with-sliding-header.header-visible {
  padding-top: 5rem; /* Adjust based on header height */
}

.content-with-sliding-header.header-hidden {
  padding-top: 0;
}

/* Scrollable areas */
.inbox-conversations-scroll {
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.inbox-messages-scroll {
  height: calc(100vh - 200px);
  overflow-y: auto;
  padding: 1rem;
}

/* Smooth scrolling */
.inbox-scrollable {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
.inbox-scrollable::-webkit-scrollbar {
  width: 6px;
}

.inbox-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.inbox-scrollable::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.inbox-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .inbox-input-container {
    background: rgba(0, 0, 0, 0.95);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .inbox-header-fixed,
  .inbox-chat-header-fixed {
    background: rgba(0, 0, 0, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .inbox-scrollable::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
  }
  
  .inbox-scrollable::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

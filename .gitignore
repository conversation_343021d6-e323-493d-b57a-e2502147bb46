node_modules
dist
.DS_Store
server/public
vite.config.ts.*
*.tar.gz
# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
# WhatsApp session data
auth_info_*
auth_info_persistent_*
.wwebjs_auth
.wwebjs_cache
**/session_*
whatsapp-web-sessions/
session_*/
_IGNORE_session_*/
cookies.txt
# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
# Cache
.cache
.replit
.local
# Database
*.db
*.sqlite
# IDE
.vscode
.idea
# OS
Thumbs.db

# managed
**.data.json
**.node-persist**
**_IGNORE_**
# end managed

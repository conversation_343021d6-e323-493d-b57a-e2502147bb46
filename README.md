# 🚀 WhatsApp Bulk Messaging Pro - Advanced Marketing Platform

A next-generation WhatsApp marketing automation platform with real-time synchronization, AI-powered agents, and enterprise-grade features. Built with modern React/TypeScript frontend and robust Node.js backend.

## ✨ Key Highlights

- 🔄 **Real-Time Synchronization**: Instant message sync with 2-second auto-refresh
- 📱 **WhatsApp Web.js Integration**: Persistent sessions with QR code authentication
- 🤖 **Multi-Provider AI Agents**: OpenAI, Claude, Gemini, Cohere, Mistral support
- 🎨 **Modern Responsive UI**: Auto-adjusting layout with animated components
- 📊 **Advanced Analytics**: Campaign tracking and performance monitoring
- 🛡️ **Anti-Blocking Protection**: Smart delays and number rotation
- 📁 **Media File Support**: Send/receive images, documents, videos with preview
- 🌐 **Socket.io Real-Time**: Live updates across all connected clients

## 🚀 Core Features

### 📨 Advanced Inbox Management
- **Real-Time Message Sync**: Auto-refresh every 2 seconds with Socket.io
- **WhatsApp-Style Interface**: Familiar chat layout with message status indicators
- **Media File Support**: Send/receive images, documents, videos with download functionality
- **Message Status Tracking**: Single tick (sent), double tick (delivered), blue tick (read)
- **Contact Management**: Block/unblock, delete conversations, contact avatars
- **Direct Messaging**: Send messages to any phone number instantly
- **Multi-Number Support**: Separate inbox for each connected WhatsApp number

### 🤖 AI Agent System
- **Multi-Provider Support**: OpenAI GPT-4o, Claude 3.5, Gemini Pro, Cohere, Mistral
- **Custom Agent Creation**: Personalized chatbots with business-specific training
- **Real-Time AI Responses**: Instant auto-replies with context awareness
- **Agent Management**: Enable/disable AI per contact with live sync
- **Business Integration**: DSA Loan Services, Customer Support, Sales automation
- **Context-Aware Conversations**: Integrates with customer data and history

### 📱 WhatsApp Integration
- **Persistent Sessions**: Maintain connections across server restarts
- **QR Code Authentication**: Instant connection with mobile WhatsApp
- **Multi-Number Support**: Connect unlimited WhatsApp business numbers
- **Session Management**: Monitor connection status and health
- **Auto-Reconnection**: Automatic session recovery and reconnection
- **Indian Number Format**: Optimized for Indian phone number handling

### 🎯 Campaign Management
- **Bulk Messaging**: Send thousands of messages with smart scheduling
- **Anti-Blocking Protection**: Human-like delays and behavior simulation
- **Number Rotation**: Intelligent load balancing across multiple numbers
- **Template System**: Reusable message templates with dynamic variables
- **Campaign Analytics**: Delivery rates, response tracking, performance metrics
- **Scheduled Campaigns**: Time-based message delivery with timezone support

### 👥 Contact Management
- **CSV Import/Export**: Bulk contact management with data validation
- **Contact Groups**: Organize contacts with tags and categories
- **Status Tracking**: Active, blocked, unsubscribed contact states
- **Duplicate Prevention**: Automatic duplicate detection and removal
- **Contact Sync**: Real-time contact updates across all sections
- **Advanced Filtering**: Search and filter contacts by multiple criteria

### 🎨 UI/UX Features
- **Responsive Design**: Auto-adjusting layout for all screen sizes and zoom levels
- **Animated Components**: Smooth transitions and interactive elements
- **Animated Emojis**: Dynamic emoji animations in chat interface
- **Modern Loading States**: Skeleton loaders and progress indicators
- **Blue & White Theme**: Professional color scheme throughout
- **Fixed Headers**: Sticky navigation and chat headers
- **Real-Time Status**: Live connection and sync indicators

### 🛡️ Security & Performance
- **Session Persistence**: Secure session management with PostgreSQL
- **Rate Limiting**: Configurable message limits and delays
- **Input Validation**: Comprehensive request validation with Zod
- **Error Handling**: Robust error boundaries and fallbacks
- **Performance Optimization**: Efficient queries and caching
- **Real-Time Updates**: Socket.io for instant synchronization

## 🛠️ Technology Stack

### Frontend Architecture
- **React 18** with TypeScript for type safety
- **Tailwind CSS** with custom animations and responsive design
- **Radix UI** components for accessibility and consistency
- **TanStack Query** for server state management and caching
- **Wouter** for lightweight client-side routing
- **Vite** for fast development and optimized builds
- **Socket.io Client** for real-time communication
- **Framer Motion** for advanced animations

### Backend Infrastructure
- **Node.js** with Express.js and TypeScript
- **Socket.io** for real-time bidirectional communication
- **whatsapp-web.js** for WhatsApp Web API integration
- **Puppeteer** for headless browser automation
- **Multer** for file upload handling
- **Express Session** with PostgreSQL store

### Database & Storage
- **PostgreSQL** with Supabase for cloud database
- **Drizzle ORM** for type-safe database operations
- **File Storage** for media files with organized directory structure
- **Session Storage** with connect-pg-simple
- **Migration System** for database schema management

### AI & External Services
- **OpenAI GPT-4o** for advanced AI responses
- **Anthropic Claude** for alternative AI provider
- **Google Gemini** for multimodal AI capabilities
- **Cohere & Mistral** for diverse AI model options
- **QR Code Generation** for WhatsApp authentication

## 🔧 Installation & Setup

### Prerequisites
- **Node.js 18+** with npm/yarn
- **PostgreSQL database** (local or cloud)
- **OpenAI API key** (for AI features)
- **Chrome/Chromium** (for Puppeteer)

### Quick Start
1. **Clone the repository**
   ```bash
   git clone https://github.com/Ank199899/whatsapp.git
   cd whatsapp-bulk-a
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   Create a `.env` file in the root directory:
   ```bash
   # Database Configuration
   DATABASE_URL=postgresql://username:password@localhost:5432/whatsapp_db

   # Session Security
   SESSION_SECRET=your_super_secret_session_key_here

   # AI Provider Keys (Optional)
   OPENAI_API_KEY=sk-your-openai-api-key
   ANTHROPIC_API_KEY=sk-ant-your-anthropic-key
   GEMINI_API_KEY=your-gemini-api-key

   # Server Configuration
   PORT=5000
   NODE_ENV=development
   ```

4. **Database Setup**
   ```bash
   # Push database schema
   npm run db:push

   # Verify database connection
   npm run check
   ```

5. **Start Development Server**
   ```bash
   # Start both frontend and backend
   npm run dev

   # Access the application at http://localhost:5173
   ```

### Production Deployment
1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start production server**
   ```bash
   npm start
   ```

3. **Process Management (Recommended)**
   ```bash
   # Using PM2 for persistent server
   npm install -g pm2
   pm2 start dist/index.js --name "whatsapp-bulk"
   pm2 startup
   pm2 save
   ```

## 🎯 Usage Guide

### 📱 Connecting WhatsApp Numbers
1. **Navigate to WhatsApp Setup**
   - Go to `/whatsapp-web-setup` or click "WhatsApp" in sidebar

2. **Connect New Number**
   - Click "Connect WhatsApp" button
   - QR code appears immediately (no delays)
   - Scan with your WhatsApp mobile app
   - Wait for "Connected" status confirmation

3. **Manage Connected Numbers**
   - View all connected/disconnected numbers
   - Delete numbers from dropdown menu
   - Monitor connection status in real-time
   - Automatic reconnection on session loss

### 🤖 Setting Up AI Agents
1. **Create New Agent**
   - Navigate to AI Agents page
   - Click "Create New Agent"
   - Choose AI provider (OpenAI, Claude, Gemini, etc.)

2. **Configure Agent Settings**
   - **Name & Role**: Custom agent identity
   - **Instructions**: Business-specific prompts
   - **Model Settings**: Temperature, max tokens
   - **Provider**: Select from multiple AI services

3. **Test & Deploy**
   - Test agent with sample messages
   - Enable/disable per contact
   - Monitor AI responses in real-time

### 📊 Campaign Management
1. **Create Message Templates**
   - Design reusable message formats
   - Add dynamic variables and placeholders
   - Organize templates by categories

2. **Import Contacts**
   - Upload CSV files with contact data
   - Automatic duplicate detection
   - Validate phone numbers (Indian format)

3. **Launch Campaigns**
   - Select target contacts and templates
   - Configure anti-blocking settings
   - Schedule delivery times
   - Monitor campaign progress

### 💬 Inbox Management
- **Real-Time Conversations**: Auto-refresh every 2 seconds
- **Direct Messaging**: Send to any phone number instantly
- **Media Support**: Send/receive images, documents, videos
- **Contact Actions**: Block/unblock, delete chats
- **AI Integration**: Toggle AI responses per contact
- **Multi-Number Inbox**: Separate conversations per WhatsApp number
- **Message Status**: Track sent, delivered, read status

## 📊 Database Schema

### Core Tables
```sql
-- User management
users: User profiles and authentication
whatsapp_numbers: Connected phone numbers with session data
user_sessions: Secure session management

-- Contact & Communication
contacts: Customer database with status tracking
conversations: Chat threads and metadata
messages: Individual message records with media support
message_status: Delivery and read receipts

-- Campaign Management
campaigns: Bulk messaging campaigns with analytics
templates: Reusable message templates
campaign_contacts: Campaign target lists

-- AI & Automation
chatbot_settings: AI agent configurations
ai_responses: AI conversation history
agent_analytics: AI performance metrics
```

### Media Support Schema
```sql
-- Media file handling
media_files: Uploaded file metadata
message_media: Links messages to media files
media_downloads: Download tracking and analytics
```

## 🤖 AI Integration

### Supported AI Providers
- **OpenAI**: GPT-4o, GPT-4 Turbo, GPT-3.5 Turbo
- **Anthropic**: Claude 3.5 Sonnet, Claude 3 Haiku, Claude 3 Opus
- **Google Gemini**: Gemini Pro, Gemini Vision, Gemini Flash
- **Cohere**: Command R+, Command R, Command Light
- **Mistral**: Mistral Large, Mistral Medium, Mistral Small

### AI Agent Configuration
```typescript
interface AIAgentConfig {
  name: string;           // "Customer Support Bot"
  role: string;           // "Support Representative"
  instructions: string;   // Business-specific prompts
  provider: string;       // "openai" | "anthropic" | "gemini"
  model: string;          // "gpt-4o" | "claude-3-5-sonnet"
  temperature: number;    // 0.0 - 1.0 creativity level
  maxTokens: number;      // Response length limit
  systemPrompt: string;   // Context and behavior rules
  isActive: boolean;      // Enable/disable agent
}
```

### Business Use Cases
- **Customer Support**: 24/7 automated support with escalation
- **Sales Assistant**: Lead qualification and product information
- **DSA Loan Services**: Loan application assistance and guidance
- **E-commerce**: Order tracking and product recommendations
- **Appointment Booking**: Schedule management and confirmations

## 🔒 Security Features

### Authentication & Authorization
- **Session Management**: Secure PostgreSQL-backed sessions with express-session
- **Input Validation**: Comprehensive request validation with Zod schemas
- **Rate Limiting**: Built-in protection against spam and abuse
- **CORS Protection**: Configured for secure cross-origin requests
- **File Upload Security**: Validated file types and size limits

### Data Protection
- **API Key Encryption**: Secure storage of user API keys
- **Session Encryption**: Encrypted session data with secure cookies
- **SQL Injection Prevention**: Parameterized queries with Drizzle ORM
- **XSS Protection**: Input sanitization and output encoding

## 📈 Performance Metrics

### Real-Time Performance
- **Message Sync**: 2-second auto-refresh intervals
- **Socket.io Updates**: Instant real-time synchronization
- **WhatsApp Response**: < 1 second message processing
- **File Upload**: Optimized media handling with progress tracking
- **Database Queries**: Indexed queries with connection pooling

### Scalability Features
- **Session Management**: Support for multiple concurrent WhatsApp sessions
- **Load Balancing**: Intelligent number rotation for campaigns
- **Caching**: TanStack Query for efficient data caching
- **Batch Processing**: Optimized bulk operations for campaigns
- **Memory Management**: Efficient resource utilization

### Anti-Blocking Protection
- **Human-like Delays**: Randomized message intervals (1-5 seconds)
- **Typing Simulation**: Realistic typing indicators
- **Number Rotation**: Automatic switching between connected numbers
- **Rate Limiting**: Configurable message limits per number/hour
- **Behavior Patterns**: Mimics natural user interaction patterns

## 🔧 Development

### Project Structure
```
whatsapp-bulk-a/
├── client/                          # React TypeScript Frontend
│   ├── src/
│   │   ├── components/             # Reusable UI components
│   │   │   ├── inbox/             # WhatsApp inbox components
│   │   │   ├── campaigns/         # Campaign management
│   │   │   ├── contacts/          # Contact management
│   │   │   ├── ai-agents/         # AI agent components
│   │   │   ├── templates/         # Message templates
│   │   │   └── ui/                # Base UI components
│   │   ├── pages/                 # Application pages/routes
│   │   ├── hooks/                 # Custom React hooks
│   │   ├── lib/                   # Utility libraries
│   │   └── styles/                # CSS and animations
│   └── index.html                 # Entry HTML file
├── server/                         # Node.js Express Backend
│   ├── index.ts                   # Main server entry point
│   ├── routes.ts                  # API route definitions
│   ├── whatsapp-web-service.ts    # WhatsApp integration
│   ├── ai-service.ts              # Multi-provider AI service
│   ├── db.ts                      # Database configuration
│   └── migrations/                # Database migrations
├── shared/                         # Shared TypeScript types
│   ├── types.ts                   # Common type definitions
│   └── validation.ts              # Zod validation schemas
├── uploads/media/                  # Media file storage
├── supabase/migrations/           # Database schema migrations
└── package.json                   # Dependencies and scripts
```

### Key Components
- **`server/whatsapp-web-service.ts`**: WhatsApp Web.js integration with session management
- **`server/ai-service.ts`**: Multi-provider AI integration (OpenAI, Claude, Gemini)
- **`client/src/components/inbox/realtime-whatsapp-inbox.tsx`**: Main inbox interface
- **`client/src/hooks/useSocket.ts`**: Real-time Socket.io integration
- **`server/routes.ts`**: RESTful API endpoints for all features

### Development Commands
```bash
# Development
npm run dev          # Start development server
npm run check        # TypeScript type checking
npm run build        # Build for production

# Database
npm run db:push      # Push schema changes to database

# Production
npm start           # Start production server
```

### Environment Setup
```bash
# Development environment
NODE_ENV=development
PORT=5000
DATABASE_URL=postgresql://localhost:5432/whatsapp_dev
SESSION_SECRET=dev_secret_key

# AI Provider Keys (Optional)
OPENAI_API_KEY=sk-your-key
ANTHROPIC_API_KEY=sk-ant-your-key
```

## 🚀 Deployment Options

### Cloud Platforms
- **Vercel**: Frontend deployment with serverless functions
- **Railway**: Full-stack deployment with PostgreSQL
- **Heroku**: Container-based deployment
- **DigitalOcean**: VPS deployment with Docker
- **AWS**: EC2 with RDS PostgreSQL

### Docker Deployment
```dockerfile
# Dockerfile example
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 5000
CMD ["npm", "start"]
```

## 📞 Support & Contributing

### Getting Help
- **Documentation**: Comprehensive guides in `/docs` folder
- **Issues**: Report bugs via GitHub Issues
- **Discussions**: Community support and feature requests

### Contributing
1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the **MIT License**. See the [LICENSE](LICENSE) file for details.

---

## 🌟 Features Roadmap

### Upcoming Features
- [ ] **Voice Messages**: Send/receive WhatsApp voice notes
- [ ] **Group Messaging**: Broadcast to WhatsApp groups
- [ ] **Advanced Analytics**: Detailed campaign performance metrics
- [ ] **Webhook Integration**: Connect with external services
- [ ] **Multi-Language Support**: Internationalization
- [ ] **Mobile App**: React Native companion app

### Recent Updates
- ✅ **Real-time Sync**: Socket.io integration for instant updates
- ✅ **Media Support**: Full media file handling with preview
- ✅ **Responsive Design**: Auto-adjusting layout for all devices
- ✅ **Animated UI**: Modern animations and transitions
- ✅ **AI Multi-Provider**: Support for 5+ AI providers
- ✅ **Session Persistence**: Reliable WhatsApp connection management

---

**🚀 Built with ❤️ for modern WhatsApp marketing automation**

*Empowering businesses with intelligent, scalable WhatsApp communication solutions.*
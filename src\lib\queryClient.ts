import { QueryClient } from "@tanstack/react-query";
import { mockApi } from "./mockData";

// Mock API request function for Vercel static deployment
export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<any> {
  // Route to appropriate mock API method based on URL
  const path = url.replace('/api/', '');

  switch (path) {
    case 'dashboard/stats':
      return mockApi.getDashboardStats();
    case 'contacts':
      if (method === 'GET') return mockApi.getContacts();
      if (method === 'POST') return mockApi.createContact(data as any);
      break;
    case 'contact-groups':
      if (method === 'GET') return mockApi.getContactGroups();
      if (method === 'POST') return mockApi.createContactGroup(data as any);
      break;
    case 'templates':
      if (method === 'GET') return mockApi.getTemplates();
      if (method === 'POST') return mockApi.createTemplate(data as any);
      break;
    case 'campaigns':
      if (method === 'GET') return mockApi.getCampaigns();
      if (method === 'POST') return mockApi.createCampaign(data as any);
      break;
    case 'whatsapp-numbers':
      return mockApi.getWhatsappNumbers();
    case 'conversations':
      return mockApi.getConversations();
    case 'chatbot/settings':
      return mockApi.getChatbotSettings();
    default:
      // Handle dynamic routes like /contacts/:id
      if (path.startsWith('contacts/') && method === 'GET') {
        const id = parseInt(path.split('/')[1]);
        return mockApi.getContact(id);
      }
      if (path.startsWith('contacts/') && method === 'PUT') {
        const id = parseInt(path.split('/')[1]);
        return mockApi.updateContact(id, data as any);
      }
      if (path.startsWith('contacts/') && method === 'DELETE') {
        const id = parseInt(path.split('/')[1]);
        return mockApi.deleteContact(id);
      }
      if (path.startsWith('conversations/') && path.includes('/messages')) {
        const conversationId = parseInt(path.split('/')[1]);
        return mockApi.getMessages(conversationId);
      }

      // Default empty response for unknown endpoints
      return {};
  }
}

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
    mutations: {
      retry: 1,
    },
  },
});

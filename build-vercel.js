#!/usr/bin/env node

// Vercel build script that excludes server dependencies
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 Starting Vercel build process...');

try {
  // Create a temporary package.json without server dependencies
  const originalPackage = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  const vercelPackage = {
    ...originalPackage,
    dependencies: {
      // Keep only frontend dependencies
      "@hookform/resolvers": originalPackage.dependencies["@hookform/resolvers"],
      "@radix-ui/react-accordion": originalPackage.dependencies["@radix-ui/react-accordion"],
      "@radix-ui/react-alert-dialog": originalPackage.dependencies["@radix-ui/react-alert-dialog"],
      "@radix-ui/react-aspect-ratio": originalPackage.dependencies["@radix-ui/react-aspect-ratio"],
      "@radix-ui/react-avatar": originalPackage.dependencies["@radix-ui/react-avatar"],
      "@radix-ui/react-checkbox": originalPackage.dependencies["@radix-ui/react-checkbox"],
      "@radix-ui/react-collapsible": originalPackage.dependencies["@radix-ui/react-collapsible"],
      "@radix-ui/react-context-menu": originalPackage.dependencies["@radix-ui/react-context-menu"],
      "@radix-ui/react-dialog": originalPackage.dependencies["@radix-ui/react-dialog"],
      "@radix-ui/react-dropdown-menu": originalPackage.dependencies["@radix-ui/react-dropdown-menu"],
      "@radix-ui/react-hover-card": originalPackage.dependencies["@radix-ui/react-hover-card"],
      "@radix-ui/react-label": originalPackage.dependencies["@radix-ui/react-label"],
      "@radix-ui/react-menubar": originalPackage.dependencies["@radix-ui/react-menubar"],
      "@radix-ui/react-navigation-menu": originalPackage.dependencies["@radix-ui/react-navigation-menu"],
      "@radix-ui/react-popover": originalPackage.dependencies["@radix-ui/react-popover"],
      "@radix-ui/react-progress": originalPackage.dependencies["@radix-ui/react-progress"],
      "@radix-ui/react-radio-group": originalPackage.dependencies["@radix-ui/react-radio-group"],
      "@radix-ui/react-scroll-area": originalPackage.dependencies["@radix-ui/react-scroll-area"],
      "@radix-ui/react-select": originalPackage.dependencies["@radix-ui/react-select"],
      "@radix-ui/react-separator": originalPackage.dependencies["@radix-ui/react-separator"],
      "@radix-ui/react-slider": originalPackage.dependencies["@radix-ui/react-slider"],
      "@radix-ui/react-slot": originalPackage.dependencies["@radix-ui/react-slot"],
      "@radix-ui/react-switch": originalPackage.dependencies["@radix-ui/react-switch"],
      "@radix-ui/react-tabs": originalPackage.dependencies["@radix-ui/react-tabs"],
      "@radix-ui/react-toast": originalPackage.dependencies["@radix-ui/react-toast"],
      "@radix-ui/react-toggle": originalPackage.dependencies["@radix-ui/react-toggle"],
      "@radix-ui/react-toggle-group": originalPackage.dependencies["@radix-ui/react-toggle-group"],
      "@radix-ui/react-tooltip": originalPackage.dependencies["@radix-ui/react-tooltip"],
      "@supabase/supabase-js": originalPackage.dependencies["@supabase/supabase-js"],
      "@tanstack/react-query": originalPackage.dependencies["@tanstack/react-query"],
      "axios": originalPackage.dependencies["axios"],
      "class-variance-authority": originalPackage.dependencies["class-variance-authority"],
      "clsx": originalPackage.dependencies["clsx"],
      "cmdk": originalPackage.dependencies["cmdk"],
      "date-fns": originalPackage.dependencies["date-fns"],
      "embla-carousel-react": originalPackage.dependencies["embla-carousel-react"],
      "framer-motion": originalPackage.dependencies["framer-motion"],
      "input-otp": originalPackage.dependencies["input-otp"],
      "lucide-react": originalPackage.dependencies["lucide-react"],
      "next-themes": originalPackage.dependencies["next-themes"],
      "react": originalPackage.dependencies["react"],
      "react-day-picker": originalPackage.dependencies["react-day-picker"],
      "react-dom": originalPackage.dependencies["react-dom"],
      "react-hook-form": originalPackage.dependencies["react-hook-form"],
      "react-icons": originalPackage.dependencies["react-icons"],
      "react-resizable-panels": originalPackage.dependencies["react-resizable-panels"],
      "recharts": originalPackage.dependencies["recharts"],
      "socket.io-client": originalPackage.dependencies["socket.io-client"],
      "tailwind-merge": originalPackage.dependencies["tailwind-merge"],
      "tailwindcss-animate": originalPackage.dependencies["tailwindcss-animate"],
      "tw-animate-css": originalPackage.dependencies["tw-animate-css"],
      "vaul": originalPackage.dependencies["vaul"],
      "wouter": originalPackage.dependencies["wouter"],
      "zod": originalPackage.dependencies["zod"]
    },
    scripts: {
      build: "vite build"
    }
  };

  // Write temporary package.json
  fs.writeFileSync('package.json.backup', JSON.stringify(originalPackage, null, 2));
  fs.writeFileSync('package.json', JSON.stringify(vercelPackage, null, 2));

  console.log('📦 Installing frontend dependencies...');
  execSync('npm install', { stdio: 'inherit' });

  console.log('🏗️ Building frontend...');
  execSync('VERCEL=1 npm run build', { stdio: 'inherit' });

  console.log('✅ Build completed successfully!');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
} finally {
  // Restore original package.json
  if (fs.existsSync('package.json.backup')) {
    fs.renameSync('package.json.backup', 'package.json');
  }
}

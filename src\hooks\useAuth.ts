import { useQuery } from "@tanstack/react-query";

export function useAuth() {
  const { data: user, isLoading, error } = useQuery({
    queryKey: ["/api/auth/user"],
    retry: (failureCount, error: any) => {
      // Don't retry on authentication errors
      if (error?.message?.includes('Unauthorized') || error?.status === 401) {
        return false;
      }
      return failureCount < 2;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });

  // Temporarily force authentication for testing
  const mockUser = {
    id: 'admin-user-123',
    email: '<EMAIL>',
    first_name: 'Admin',
    last_name: 'User',
    profile_image_url: null
  };

  return {
    user: user || mockUser,
    isLoading: false, // Force loading to false
    isAuthenticated: true, // Force authenticated to true
    error: null,
  };
}

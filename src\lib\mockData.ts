// Mock data service for Vercel static deployment
export interface Contact {
  id: number;
  name: string;
  phone: string;
  phone_number?: string;
  email?: string;
  group_id?: number;
  status?: string;
  created_at: string;
  ai_enabled?: boolean;
  blocked?: boolean;
}

export interface ContactGroup {
  id: number;
  name: string;
  description?: string;
  color?: string;
  contact_count?: number;
  created_at: string;
}

export interface Template {
  id: number;
  name: string;
  content: string;
  category?: string;
  variables?: string[];
  created_at: string;
  usage_count?: number;
}

export interface Campaign {
  id: number;
  name: string;
  status: string;
  created_at: string;
  scheduled_at?: string;
  template_id?: number;
  contact_count?: number;
  sent_count?: number;
  delivered_count?: number;
  read_count?: number;
}

export interface WhatsappNumber {
  id: number;
  phone: string;
  phone_number?: string;
  name?: string;
  display_name?: string;
  status: string;
  connected_at?: string;
  last_seen?: string;
}

export interface WhatsAppWebSession {
  sessionId: string;
  status: string;
  phoneNumber?: string;
  userId: string;
  createdAt: string;
  lastActivity?: string;
  displayName?: string;
  profilePicture?: string;
}

export interface Message {
  id: number;
  conversation_id: number;
  content: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'document';
  direction: 'incoming' | 'outgoing';
  status?: 'sent' | 'delivered' | 'read';
  timestamp: string;
  from_number?: string;
  to_number?: string;
}

export interface Conversation {
  id: number;
  contact_id: number;
  whatsapp_number_id: number;
  last_message?: string;
  last_message_at?: string;
  unread_count?: number;
  ai_enabled?: boolean;
  contact?: Contact;
  whatsapp_number?: WhatsappNumber;
}

export interface DashboardStats {
  total_contacts: number;
  total_campaigns: number;
  total_templates: number;
  active_whatsapp_numbers: number;
  messages_sent_today: number;
  messages_delivered_today: number;
  campaign_success_rate: number;
  recent_activity: Array<{
    id: number;
    type: string;
    description: string;
    timestamp: string;
  }>;
}

// Mock data
export const mockContacts: Contact[] = [
  {
    id: 1,
    name: "Rahul Sharma",
    phone: "+919876543210",
    phone_number: "+919876543210",
    email: "<EMAIL>",
    group_id: 1,
    status: "active",
    created_at: "2024-01-15T10:30:00Z",
    ai_enabled: true,
    blocked: false
  },
  {
    id: 2,
    name: "Priya Patel",
    phone: "+919876543211",
    phone_number: "+919876543211",
    email: "<EMAIL>",
    group_id: 1,
    status: "active",
    created_at: "2024-01-16T14:20:00Z",
    ai_enabled: false,
    blocked: false
  },
  {
    id: 3,
    name: "Amit Kumar",
    phone: "+919876543212",
    phone_number: "+919876543212",
    email: "<EMAIL>",
    group_id: 2,
    status: "active",
    created_at: "2024-01-17T09:15:00Z",
    ai_enabled: true,
    blocked: false
  },
  {
    id: 4,
    name: "Sneha Gupta",
    phone: "+919876543213",
    phone_number: "+919876543213",
    email: "<EMAIL>",
    group_id: 2,
    status: "active",
    created_at: "2024-01-18T16:45:00Z",
    ai_enabled: false,
    blocked: false
  },
  {
    id: 5,
    name: "Vikash Singh",
    phone: "+919876543214",
    phone_number: "+919876543214",
    email: "<EMAIL>",
    group_id: 1,
    status: "active",
    created_at: "2024-01-19T11:30:00Z",
    ai_enabled: true,
    blocked: false
  }
];

export const mockContactGroups: ContactGroup[] = [
  {
    id: 1,
    name: "Premium Customers",
    description: "High-value customers for premium campaigns",
    color: "#3B82F6",
    contact_count: 3,
    created_at: "2024-01-10T08:00:00Z"
  },
  {
    id: 2,
    name: "New Leads",
    description: "Recently acquired leads for nurturing",
    color: "#10B981",
    contact_count: 2,
    created_at: "2024-01-12T10:00:00Z"
  },
  {
    id: 3,
    name: "VIP Clients",
    description: "VIP clients requiring special attention",
    color: "#F59E0B",
    contact_count: 0,
    created_at: "2024-01-14T12:00:00Z"
  }
];

export const mockTemplates: Template[] = [
  {
    id: 1,
    name: "Welcome Message",
    content: "Hello {{name}}! Welcome to our service. We're excited to have you on board!",
    category: "Welcome",
    variables: ["name"],
    created_at: "2024-01-10T08:00:00Z",
    usage_count: 25
  },
  {
    id: 2,
    name: "Product Launch",
    content: "🚀 Exciting news {{name}}! Our new {{product}} is now available. Get 20% off with code LAUNCH20!",
    category: "Marketing",
    variables: ["name", "product"],
    created_at: "2024-01-12T10:00:00Z",
    usage_count: 18
  },
  {
    id: 3,
    name: "Follow Up",
    content: "Hi {{name}}, just checking in to see how you're enjoying our service. Any questions?",
    category: "Support",
    variables: ["name"],
    created_at: "2024-01-14T12:00:00Z",
    usage_count: 12
  },
  {
    id: 4,
    name: "Special Offer",
    content: "🎉 Special offer for {{name}}! Get {{discount}}% off your next purchase. Valid until {{expiry}}!",
    category: "Promotion",
    variables: ["name", "discount", "expiry"],
    created_at: "2024-01-16T14:00:00Z",
    usage_count: 8
  }
];

export const mockCampaigns: Campaign[] = [
  {
    id: 1,
    name: "New Year Promotion",
    status: "completed",
    created_at: "2024-01-01T00:00:00Z",
    scheduled_at: "2024-01-01T09:00:00Z",
    template_id: 2,
    contact_count: 150,
    sent_count: 150,
    delivered_count: 145,
    read_count: 120
  },
  {
    id: 2,
    name: "Product Launch Campaign",
    status: "active",
    created_at: "2024-01-15T10:00:00Z",
    scheduled_at: "2024-01-20T10:00:00Z",
    template_id: 2,
    contact_count: 200,
    sent_count: 180,
    delivered_count: 175,
    read_count: 140
  },
  {
    id: 3,
    name: "Customer Follow-up",
    status: "scheduled",
    created_at: "2024-01-18T14:00:00Z",
    scheduled_at: "2024-01-25T11:00:00Z",
    template_id: 3,
    contact_count: 75,
    sent_count: 0,
    delivered_count: 0,
    read_count: 0
  }
];

export const mockWhatsappNumbers: WhatsappNumber[] = [
  {
    id: 1,
    phone: "+919876543200",
    phone_number: "+919876543200",
    name: "Business Main",
    display_name: "Business Main",
    status: "connected",
    connected_at: "2024-01-10T08:00:00Z",
    last_seen: "2024-01-20T15:30:00Z"
  },
  {
    id: 2,
    phone: "+919876543201",
    phone_number: "+919876543201",
    name: "Support Line",
    display_name: "Support Line",
    status: "connected",
    connected_at: "2024-01-12T10:00:00Z",
    last_seen: "2024-01-20T14:45:00Z"
  },
  {
    id: 3,
    phone: "+919876543202",
    phone_number: "+919876543202",
    name: "Marketing",
    display_name: "Marketing",
    status: "disconnected",
    connected_at: "2024-01-14T12:00:00Z",
    last_seen: "2024-01-19T18:20:00Z"
  }
];

export const mockMessages: Message[] = [
  {
    id: 1,
    conversation_id: 1,
    content: "Hello! I'm interested in your products.",
    type: "text",
    direction: "incoming",
    status: "read",
    timestamp: "2024-01-20T10:30:00Z",
    from_number: "+919876543210",
    to_number: "+919876543200"
  },
  {
    id: 2,
    conversation_id: 1,
    content: "Thank you for your interest! I'd be happy to help you. What specific products are you looking for?",
    type: "text",
    direction: "outgoing",
    status: "read",
    timestamp: "2024-01-20T10:32:00Z",
    from_number: "+919876543200",
    to_number: "+919876543210"
  },
  {
    id: 3,
    conversation_id: 2,
    content: "Hi, I need support with my recent order.",
    type: "text",
    direction: "incoming",
    status: "delivered",
    timestamp: "2024-01-20T14:15:00Z",
    from_number: "+919876543211",
    to_number: "+919876543201"
  },
  {
    id: 4,
    conversation_id: 2,
    content: "I'll be happy to help you with your order. Can you please provide your order number?",
    type: "text",
    direction: "outgoing",
    status: "delivered",
    timestamp: "2024-01-20T14:17:00Z",
    from_number: "+919876543201",
    to_number: "+919876543211"
  }
];

export const mockConversations: Conversation[] = [
  {
    id: 1,
    contact_id: 1,
    whatsapp_number_id: 1,
    last_message: "Thank you for your interest! I'd be happy to help you.",
    last_message_at: "2024-01-20T10:32:00Z",
    unread_count: 0,
    ai_enabled: true,
    contact: mockContacts[0],
    whatsapp_number: mockWhatsappNumbers[0]
  },
  {
    id: 2,
    contact_id: 2,
    whatsapp_number_id: 2,
    last_message: "I'll be happy to help you with your order.",
    last_message_at: "2024-01-20T14:17:00Z",
    unread_count: 1,
    ai_enabled: false,
    contact: mockContacts[1],
    whatsapp_number: mockWhatsappNumbers[1]
  },
  {
    id: 3,
    contact_id: 3,
    whatsapp_number_id: 1,
    last_message: "Welcome to our service!",
    last_message_at: "2024-01-19T16:45:00Z",
    unread_count: 0,
    ai_enabled: true,
    contact: mockContacts[2],
    whatsapp_number: mockWhatsappNumbers[0]
  }
];

export const mockDashboardStats: DashboardStats = {
  total_contacts: 5,
  total_campaigns: 3,
  total_templates: 4,
  active_whatsapp_numbers: 2,
  messages_sent_today: 45,
  messages_delivered_today: 42,
  campaign_success_rate: 85.5,
  recent_activity: [
    {
      id: 1,
      type: "message",
      description: "New message from Rahul Sharma",
      timestamp: "2024-01-20T15:30:00Z"
    },
    {
      id: 2,
      type: "campaign",
      description: "Product Launch Campaign started",
      timestamp: "2024-01-20T10:00:00Z"
    },
    {
      id: 3,
      type: "contact",
      description: "New contact added: Vikash Singh",
      timestamp: "2024-01-19T11:30:00Z"
    },
    {
      id: 4,
      type: "template",
      description: "Special Offer template created",
      timestamp: "2024-01-16T14:00:00Z"
    }
  ]
};

// Mock API service
export class MockApiService {
  // Simulate API delay
  private async delay(ms: number = 500): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Dashboard stats
  async getDashboardStats(): Promise<DashboardStats> {
    await this.delay(300);
    return mockDashboardStats;
  }

  // Contacts
  async getContacts(): Promise<Contact[]> {
    await this.delay(400);
    return mockContacts;
  }

  async getContact(id: number): Promise<Contact | null> {
    await this.delay(200);
    return mockContacts.find(c => c.id === id) || null;
  }

  async createContact(data: Partial<Contact>): Promise<Contact> {
    await this.delay(600);
    const newContact: Contact = {
      id: Math.max(...mockContacts.map(c => c.id)) + 1,
      name: data.name || "",
      phone: data.phone || "",
      phone_number: data.phone || "",
      email: data.email,
      group_id: data.group_id,
      status: "active",
      created_at: new Date().toISOString(),
      ai_enabled: false,
      blocked: false
    };
    mockContacts.push(newContact);
    return newContact;
  }

  async updateContact(id: number, data: Partial<Contact>): Promise<Contact | null> {
    await this.delay(500);
    const index = mockContacts.findIndex(c => c.id === id);
    if (index === -1) return null;

    mockContacts[index] = { ...mockContacts[index], ...data };
    return mockContacts[index];
  }

  async deleteContact(id: number): Promise<boolean> {
    await this.delay(400);
    const index = mockContacts.findIndex(c => c.id === id);
    if (index === -1) return false;

    mockContacts.splice(index, 1);
    return true;
  }

  // Contact Groups
  async getContactGroups(): Promise<ContactGroup[]> {
    await this.delay(300);
    return mockContactGroups;
  }

  async createContactGroup(data: Partial<ContactGroup>): Promise<ContactGroup> {
    await this.delay(500);
    const newGroup: ContactGroup = {
      id: Math.max(...mockContactGroups.map(g => g.id)) + 1,
      name: data.name || "",
      description: data.description,
      color: data.color || "#3B82F6",
      contact_count: 0,
      created_at: new Date().toISOString()
    };
    mockContactGroups.push(newGroup);
    return newGroup;
  }

  // Templates
  async getTemplates(): Promise<Template[]> {
    await this.delay(350);
    return mockTemplates;
  }

  async createTemplate(data: Partial<Template>): Promise<Template> {
    await this.delay(600);
    const newTemplate: Template = {
      id: Math.max(...mockTemplates.map(t => t.id)) + 1,
      name: data.name || "",
      content: data.content || "",
      category: data.category,
      variables: data.variables || [],
      created_at: new Date().toISOString(),
      usage_count: 0
    };
    mockTemplates.push(newTemplate);
    return newTemplate;
  }

  // Campaigns
  async getCampaigns(): Promise<Campaign[]> {
    await this.delay(400);
    return mockCampaigns;
  }

  async createCampaign(data: Partial<Campaign>): Promise<Campaign> {
    await this.delay(700);
    const newCampaign: Campaign = {
      id: Math.max(...mockCampaigns.map(c => c.id)) + 1,
      name: data.name || "",
      status: "scheduled",
      created_at: new Date().toISOString(),
      scheduled_at: data.scheduled_at,
      template_id: data.template_id,
      contact_count: 0,
      sent_count: 0,
      delivered_count: 0,
      read_count: 0
    };
    mockCampaigns.push(newCampaign);
    return newCampaign;
  }

  // WhatsApp Numbers
  async getWhatsappNumbers(): Promise<WhatsappNumber[]> {
    await this.delay(300);
    return mockWhatsappNumbers;
  }

  // Conversations
  async getConversations(): Promise<Conversation[]> {
    await this.delay(450);
    return mockConversations;
  }

  async getMessages(conversationId: number): Promise<Message[]> {
    await this.delay(250);
    return mockMessages.filter(m => m.conversation_id === conversationId);
  }

  // AI Chatbot settings
  async getChatbotSettings(): Promise<any> {
    await this.delay(200);
    return {
      enabled: true,
      auto_reply: true,
      business_hours_only: false,
      welcome_message: "Hello! How can I help you today?",
      fallback_message: "I'm sorry, I didn't understand. A human agent will assist you shortly."
    };
  }
}

export const mockApi = new MockApiService();

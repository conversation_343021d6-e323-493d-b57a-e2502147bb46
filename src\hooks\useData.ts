import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { mockApi, type Contact, type ContactGroup, type Template, type Campaign, type WhatsappNumber, type Conversation, type DashboardStats } from "../lib/mockData";

// Dashboard hooks
export function useDashboardStats() {
  return useQuery({
    queryKey: ["dashboard", "stats"],
    queryFn: () => mockApi.getDashboardStats(),
    staleTime: 1000 * 60 * 2, // 2 minutes
  });
}

// Contact hooks
export function useContacts() {
  return useQuery({
    queryKey: ["contacts"],
    queryFn: () => mockApi.getContacts(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

export function useContact(id: number) {
  return useQuery({
    queryKey: ["contacts", id],
    queryFn: () => mockApi.getContact(id),
    enabled: !!id,
  });
}

export function useCreateContact() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Partial<Contact>) => mockApi.createContact(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contacts"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard", "stats"] });
    },
  });
}

export function useUpdateContact() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<Contact> }) => 
      mockApi.updateContact(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["contacts"] });
      queryClient.invalidateQueries({ queryKey: ["contacts", id] });
    },
  });
}

export function useDeleteContact() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: number) => mockApi.deleteContact(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contacts"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard", "stats"] });
    },
  });
}

// Contact Group hooks
export function useContactGroups() {
  return useQuery({
    queryKey: ["contact-groups"],
    queryFn: () => mockApi.getContactGroups(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

export function useCreateContactGroup() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Partial<ContactGroup>) => mockApi.createContactGroup(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contact-groups"] });
    },
  });
}

// Template hooks
export function useTemplates() {
  return useQuery({
    queryKey: ["templates"],
    queryFn: () => mockApi.getTemplates(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

export function useCreateTemplate() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Partial<Template>) => mockApi.createTemplate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["templates"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard", "stats"] });
    },
  });
}

// Campaign hooks
export function useCampaigns() {
  return useQuery({
    queryKey: ["campaigns"],
    queryFn: () => mockApi.getCampaigns(),
    staleTime: 1000 * 60 * 3, // 3 minutes
  });
}

export function useCreateCampaign() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Partial<Campaign>) => mockApi.createCampaign(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["campaigns"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard", "stats"] });
    },
  });
}

// WhatsApp Number hooks
export function useWhatsappNumbers() {
  return useQuery({
    queryKey: ["whatsapp-numbers"],
    queryFn: () => mockApi.getWhatsappNumbers(),
    staleTime: 1000 * 60 * 2, // 2 minutes
  });
}

// Conversation hooks
export function useConversations() {
  return useQuery({
    queryKey: ["conversations"],
    queryFn: () => mockApi.getConversations(),
    staleTime: 1000 * 60 * 1, // 1 minute
  });
}

export function useMessages(conversationId: number) {
  return useQuery({
    queryKey: ["conversations", conversationId, "messages"],
    queryFn: () => mockApi.getMessages(conversationId),
    enabled: !!conversationId,
    staleTime: 1000 * 30, // 30 seconds
  });
}

// AI Chatbot hooks
export function useChatbotSettings() {
  return useQuery({
    queryKey: ["chatbot", "settings"],
    queryFn: () => mockApi.getChatbotSettings(),
    staleTime: 1000 * 60 * 10, // 10 minutes
  });
}

// Analytics hooks
export function useAnalytics() {
  return useQuery({
    queryKey: ["analytics"],
    queryFn: async () => {
      // Generate analytics data from existing mock data
      const campaigns = await mockApi.getCampaigns();
      const contacts = await mockApi.getContacts();
      const templates = await mockApi.getTemplates();
      
      return {
        totalCampaigns: campaigns.length,
        activeCampaigns: campaigns.filter(c => c.status === 'active').length,
        totalContacts: contacts.length,
        totalTemplates: templates.length,
        campaignPerformance: campaigns.map(campaign => ({
          id: campaign.id,
          name: campaign.name,
          sent: campaign.sent_count || 0,
          delivered: campaign.delivered_count || 0,
          read: campaign.read_count || 0,
          deliveryRate: campaign.sent_count ? 
            Math.round(((campaign.delivered_count || 0) / campaign.sent_count) * 100) : 0,
          readRate: campaign.delivered_count ? 
            Math.round(((campaign.read_count || 0) / campaign.delivered_count) * 100) : 0,
        })),
        monthlyStats: [
          { month: 'Jan', sent: 1250, delivered: 1180, read: 950 },
          { month: 'Feb', sent: 1400, delivered: 1320, read: 1100 },
          { month: 'Mar', sent: 1600, delivered: 1520, read: 1280 },
          { month: 'Apr', sent: 1350, delivered: 1290, read: 1050 },
          { month: 'May', sent: 1800, delivered: 1710, read: 1420 },
          { month: 'Jun', sent: 1950, delivered: 1850, read: 1580 },
        ],
        topPerformingTemplates: templates
          .sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0))
          .slice(0, 5)
          .map(template => ({
            id: template.id,
            name: template.name,
            category: template.category || 'General',
            usageCount: template.usage_count || 0,
            successRate: Math.floor(Math.random() * 30) + 70, // Random success rate 70-100%
          })),
      };
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Bulk operations
export function useBulkContactUpload() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (contacts: Partial<Contact>[]) => {
      // Simulate bulk upload
      const results = [];
      for (const contact of contacts) {
        const result = await mockApi.createContact(contact);
        results.push(result);
      }
      return results;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contacts"] });
      queryClient.invalidateQueries({ queryKey: ["dashboard", "stats"] });
    },
  });
}

// Real-time sync simulation (for compatibility with existing components)
export function useRealtimeSync() {
  return {
    isConnected: true,
    lastSync: new Date(),
    syncStatus: 'connected' as const,
  };
}

export function useGlobalSync() {
  return {
    isConnected: true,
    lastSync: new Date(),
    syncStatus: 'connected' as const,
  };
}

export function useComprehensiveRealTimeSync() {
  return {
    isConnected: true,
    lastSync: new Date(),
    syncStatus: 'connected' as const,
  };
}

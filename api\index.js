// Vercel Serverless Function Entry Point
// Note: WhatsApp Web.js with Puppeteer is not compatible with Vercel serverless
// This is a simplified API for frontend-only features

import express from 'express';
import cors from 'cors';
import { createClient } from '@supabase/supabase-js';

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL || '',
  process.env.SUPABASE_ANON_KEY || ''
);

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: 'API is running on Vercel',
    timestamp: new Date().toISOString()
  });
});

// Get contacts
app.get('/api/contacts', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('contacts')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    res.json(data || []);
  } catch (error) {
    console.error('Error fetching contacts:', error);
    res.status(500).json({ error: 'Failed to fetch contacts' });
  }
});

// Add contact
app.post('/api/contacts', async (req, res) => {
  try {
    const { name, phone, email, tags } = req.body;
    
    const { data, error } = await supabase
      .from('contacts')
      .insert([{ name, phone, email, tags }])
      .select();

    if (error) throw error;
    res.json(data[0]);
  } catch (error) {
    console.error('Error adding contact:', error);
    res.status(500).json({ error: 'Failed to add contact' });
  }
});

// Get templates
app.get('/api/templates', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('templates')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    res.json(data || []);
  } catch (error) {
    console.error('Error fetching templates:', error);
    res.status(500).json({ error: 'Failed to fetch templates' });
  }
});

// WhatsApp features (limited on Vercel)
app.get('/api/whatsapp/status', (req, res) => {
  res.json({ 
    message: 'WhatsApp Web.js requires a persistent server environment. Consider using Railway, Render, or VPS for full WhatsApp functionality.',
    recommendation: 'Deploy to Railway or Render for WhatsApp features'
  });
});

// Export for Vercel
export default app;

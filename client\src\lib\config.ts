// Production configuration for Vercel deployment
export const config = {
  // API Base URL - Static deployment configuration
  apiBaseUrl: '',

  // Features available in production static deployment
  features: {
    whatsappIntegration: false, // Requires backend server
    realTimeSync: false, // Requires backend server
    mediaUpload: false, // Requires backend server
    qrCodeGeneration: false, // Requires backend server
    contactManagement: true, // Frontend-only functionality
    templateManagement: true, // Frontend-only functionality
    campaignPlanning: true, // Frontend-only functionality
    analytics: true, // Frontend-only functionality
  },

  // Production environment
  environment: {
    isVercel: true,
    isProduction: true,
    isDevelopment: false,
  }
};

// Helper function to check if a feature is available
export const isFeatureAvailable = (feature: keyof typeof config.features): boolean => {
  return config.features[feature];
};

// Helper function to get appropriate API endpoint
export const getApiUrl = (endpoint: string): string => {
  return `${config.apiBaseUrl}/api${endpoint}`;
};

// Configuration for different environments
export const config = {
  // API Base URL
  apiBaseUrl: process.env.NODE_ENV === 'production' 
    ? (process.env.VERCEL === '1' 
        ? '' // Vercel serverless functions
        : 'https://your-backend-url.railway.app') // Railway backend
    : 'http://localhost:5000',

  // Features available in different environments
  features: {
    whatsappIntegration: process.env.VERCEL !== '1', // Not available on Vercel
    realTimeSync: process.env.VERCEL !== '1',
    mediaUpload: process.env.VERCEL !== '1',
    qrCodeGeneration: process.env.VERCEL !== '1',
    
    // Always available features
    contactManagement: true,
    templateManagement: true,
    campaignPlanning: true,
    analytics: true,
  },

  // Environment info
  environment: {
    isVercel: process.env.VERCEL === '1',
    isProduction: process.env.NODE_ENV === 'production',
    isDevelopment: process.env.NODE_ENV === 'development',
  }
};

// Helper function to check if a feature is available
export const isFeatureAvailable = (feature: keyof typeof config.features): boolean => {
  return config.features[feature];
};

// Helper function to get appropriate API endpoint
export const getApiUrl = (endpoint: string): string => {
  return `${config.apiBaseUrl}/api${endpoint}`;
};

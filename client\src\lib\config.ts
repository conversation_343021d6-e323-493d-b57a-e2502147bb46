// Configuration for production environment
export const config = {
  // API Base URL - Vercel deployment
  apiBaseUrl: '',

  // Features available in production
  features: {
    whatsappIntegration: true,
    realTimeSync: true,
    mediaUpload: true,
    qrCodeGeneration: true,
    contactManagement: true,
    templateManagement: true,
    campaignPlanning: true,
    analytics: true,
  },

  // Environment info
  environment: {
    isVercel: true,
    isProduction: true,
    isDevelopment: false,
  }
};

// Helper function to check if a feature is available
export const isFeatureAvailable = (feature: keyof typeof config.features): boolean => {
  return config.features[feature];
};

// Helper function to get appropriate API endpoint
export const getApiUrl = (endpoint: string): string => {
  return `${config.apiBaseUrl}/api${endpoint}`;
};

// Configuration for Vercel static deployment
export const config = {
  // API Base URL - For static deployment, we'll use a placeholder or external API
  apiBaseUrl: process.env.NODE_ENV === 'production'
    ? 'https://your-backend-api.com' // Replace with your actual backend URL
    : 'http://localhost:5000',

  // Features available in static deployment
  features: {
    whatsappIntegration: false, // Disabled for static deployment
    realTimeSync: false, // Disabled for static deployment
    mediaUpload: false, // Disabled for static deployment
    qrCodeGeneration: false, // Disabled for static deployment
    contactManagement: true, // Can work with external API
    templateManagement: true, // Can work with external API
    campaignPlanning: true, // Can work with external API
    analytics: true, // Can work with external API
  },

  // Environment info
  environment: {
    isVercel: true,
    isProduction: true,
    isDevelopment: false,
  }
};

// Helper function to check if a feature is available
export const isFeatureAvailable = (feature: keyof typeof config.features): boolean => {
  return config.features[feature];
};

// Helper function to get appropriate API endpoint
export const getApiUrl = (endpoint: string): string => {
  return `${config.apiBaseUrl}/api${endpoint}`;
};

import React, { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, UserPlus, MessageCircle, TrendingUp } from "lucide-react";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import ContactTable from "@/components/contacts/contact-table";
import { useContacts } from "@/hooks/useData";

interface ContactStats {
  total: number;
  newThisMonth: number;
  activeChats: number;
  totalTags: number;
}

export default function ContactsPage() {
  // Fetch contacts data
  const { data: contacts, isLoading } = useContacts();

  // Calculate stats from contacts data
  const stats: ContactStats = {
    total: contacts?.length || 0,
    newThisMonth: contacts?.filter(contact => {
      const createdDate = new Date(contact.created_at);
      const now = new Date();
      return createdDate.getMonth() === now.getMonth() &&
             createdDate.getFullYear() === now.getFullYear();
    }).length || 0,
    activeChats: contacts?.filter(contact => contact.status === 'active').length || 0,
    totalTags: new Set(contacts?.map(contact => contact.group_id).filter(Boolean)).size || 0,
  };
          
          const now = new Date();
          const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          
          const newThisMonth = contacts.filter((contact: any) => 
            new Date(contact.createdAt) >= thisMonth
          ).length;
          
          const allTags = new Set();
          contacts.forEach((contact: any) => {
            contact.tags?.forEach((tag: string) => allTags.add(tag));
          });


  // Auto-sync with WhatsApp contacts (placeholder for future implementation)
  useEffect(() => {
    const syncWithWhatsApp = async () => {
      try {
        // This will be implemented when WhatsApp integration is ready
        console.log("🔄 Auto-syncing with WhatsApp contacts...");
        
        // For now, just refresh the contacts
        // In the future, this will sync with WhatsApp Web API
      } catch (error) {
        console.error("WhatsApp sync error:", error);
      }
    };

    // Sync every 5 minutes
    const interval = setInterval(syncWithWhatsApp, 5 * 60 * 1000);
    
    // Initial sync
    syncWithWhatsApp();
    
    return () => clearInterval(interval);
  }, []);

  const statsCards = [
    {
      title: "Total Contacts",
      value: stats?.total || 0,
      icon: Users,
      description: "All contacts in your database",
      color: "text-blue-600",
    },
    {
      title: "New This Month",
      value: stats?.newThisMonth || 0,
      icon: UserPlus,
      description: "Contacts added this month",
      color: "text-blue-500",
    },
    {
      title: "Active Chats",
      value: stats?.activeChats || 0,
      icon: MessageCircle,
      description: "Contacts with recent messages",
      color: "text-blue-600",
    },
    {
      title: "Total Tags",
      value: stats?.totalTags || 0,
      icon: TrendingUp,
      description: "Unique tags used",
      color: "text-blue-700",
    },
  ];

  return (
    <div className="flex h-screen bg-blue-50">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header
          title="Contacts"
          subtitle="Manage your contact database with real-time WhatsApp sync"
          primaryAction={{
            label: "Add Contact",
            onClick: () => {
              // This will be handled by the ContactTable component
              const addButton = document.querySelector('[data-testid="add-contact-button"]') as HTMLButtonElement;
              if (addButton) addButton.click();
            }
          }}
        />
        <main className="flex-1 overflow-auto p-6">
          {/* Auto-sync status */}
          <div className="mb-6 flex justify-end">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              Auto-sync enabled
            </Badge>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {statsCards.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <Card key={index}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {stat.title}
                    </CardTitle>
                    <IconComponent className={`h-4 w-4 ${stat.color}`} />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stat.value.toLocaleString()}</div>
                    <p className="text-xs text-gray-600 mt-1">
                      {stat.description}
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Contact Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Contact Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ContactTable />
            </CardContent>
          </Card>

          {/* Real-time sync indicator */}
          <div className="text-center text-sm text-gray-500 mt-6">
            <div className="flex items-center justify-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              Real-time sync active • Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
